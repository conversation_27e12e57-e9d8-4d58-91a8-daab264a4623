import { sql } from "kysely";
import { db } from "@/database/connection";
import type { AttendanceStatResponseDTO, DashboardResponseDTO } from "../dtos";

// Helper untuk hitung trendType
const getTrendType = (value: number) =>
	value > 0 ? "increase" : value < 0 ? "decrease" : "neutral";

export const getDashboardData = async (): Promise<DashboardResponseDTO> => {
	// Get current date for calculations
	const now = new Date();
	const today = now.toISOString().split("T")[0] as string; // YYYY-MM-DD format
	const yesterdayDate = new Date(now);
	yesterdayDate.setDate(now.getDate() - 1);
	const yesterday = yesterdayDate.toISOString().split("T")[0] as string;

	// === USERS DATA CALCULATIONS ===

	// Total employees
	const { total: totalEmployees } = await db
		.selectFrom("users")
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	const { total: totalEmployeesYesterday } = await db
		.selectFrom("users")
		.where(sql`DATE(created_at)`, "<", today)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	// Total employees present today (checked in)
	const { total: totalPresent } = await db
		.selectFrom("attendanceLogs")
		.where("logDate", "=", today)
		.where("type", "=", "CHECK_IN")
		.select(sql<number>`count(distinct user_id)`.as("total"))
		.executeTakeFirstOrThrow();

	const { total: totalPresentYesterday } = await db
		.selectFrom("attendanceLogs")
		.where("logDate", "=", yesterday)
		.where("type", "=", "CHECK_IN")
		.select(sql<number>`count(distinct user_id)`.as("total"))
		.executeTakeFirstOrThrow();

	// Total employees not present today (total employees - present employees)
	const totalNotPresent = totalEmployees - totalPresent;
	const totalNotPresentYesterday =
		totalEmployeesYesterday - totalPresentYesterday;

	// Total employees on leave today (approved leave requests)
	const { total: totalUsersLeave } = await db
		.selectFrom("leaveRequests")
		.where("status", "=", "APPROVED")
		.where("startDate", "<=", today)
		.where("endDate", ">=", today)
		.select(sql<number>`count(distinct user_id)`.as("total"))
		.executeTakeFirstOrThrow();

	const { total: totalUsersLeaveYesterday } = await db
		.selectFrom("leaveRequests")
		.where("status", "=", "APPROVED")
		.where("startDate", "<=", yesterday)
		.where("endDate", ">=", yesterday)
		.select(sql<number>`count(distinct user_id)`.as("total"))
		.executeTakeFirstOrThrow();

	// === ATTENDANCE DATA CALCULATIONS ===

	// Get attendance rules for determining on-time vs late
	const attendanceRules = await db
		.selectFrom("attendanceRules")
		.select(["checkInEndTime", "checkOutStartTime"])
		.executeTakeFirst();

	const checkInEndTime = attendanceRules?.checkInEndTime || "09:00:00";
	const checkOutStartTime = attendanceRules?.checkOutStartTime || "17:00:00";

	// Total employees who came on time today
	const { total: totalOnTime } = await db
		.selectFrom("attendanceLogs")
		.where("logDate", "=", today)
		.where("type", "=", "CHECK_IN")
		.where("logTime", "<=", checkInEndTime)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	const { total: totalOnTimeYesterday } = await db
		.selectFrom("attendanceLogs")
		.where("logDate", "=", yesterday)
		.where("type", "=", "CHECK_IN")
		.where("logTime", "<=", checkInEndTime)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	// Total employees who came late today
	const { total: totalLate } = await db
		.selectFrom("attendanceLogs")
		.where("logDate", "=", today)
		.where("type", "=", "CHECK_IN")
		.where("logTime", ">", checkInEndTime)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	const { total: totalLateYesterday } = await db
		.selectFrom("attendanceLogs")
		.where("logDate", "=", yesterday)
		.where("type", "=", "CHECK_IN")
		.where("logTime", ">", checkInEndTime)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	// Total employees who checked out on time today
	const { total: totalCheckoutOnTime } = await db
		.selectFrom("attendanceLogs")
		.where("logDate", "=", today)
		.where("type", "=", "CHECK_OUT")
		.where("logTime", ">=", checkOutStartTime)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	const { total: totalCheckoutOnTimeYesterday } = await db
		.selectFrom("attendanceLogs")
		.where("logDate", "=", yesterday)
		.where("type", "=", "CHECK_OUT")
		.where("logTime", ">=", checkOutStartTime)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	// Total employees who checked out early today
	const { total: totalCheckoutEarly } = await db
		.selectFrom("attendanceLogs")
		.where("logDate", "=", today)
		.where("type", "=", "CHECK_OUT")
		.where("logTime", "<", checkOutStartTime)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	const { total: totalCheckoutEarlyYesterday } = await db
		.selectFrom("attendanceLogs")
		.where("logDate", "=", yesterday)
		.where("type", "=", "CHECK_OUT")
		.where("logTime", "<", checkOutStartTime)
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	// === LATEST LEAVE REQUESTS ===
	const latestLeaveRequests = await db
		.selectFrom("leaveRequests")
		.innerJoin("users", "users.id", "leaveRequests.userId")
		.innerJoin(
			"leavePolicies",
			"leavePolicies.id",
			"leaveRequests.leavePolicyId",
		)
		.leftJoin("users as reviewer", "reviewer.id", "leaveRequests.reviewedBy")
		.selectAll("leaveRequests")
		.select([
			"leavePolicies.name",
			"leavePolicies.isCountedAsPresent",
			"users.name as userName",
			"users.email as userEmail",
			"reviewer.name as reviewerName",
			"reviewer.email as reviewerEmail",
		])
		.orderBy("leaveRequests.createdAt", "desc")
		.limit(5)
		.execute();

	// === LATEST OFFICE LEAVES ===
	const latestOfficeLeaves = await db
		.selectFrom("officeLeaves")
		.innerJoin("users", "users.id", "officeLeaves.userId")
		.leftJoin("users as reviewer", "reviewer.id", "officeLeaves.reviewedBy")
		.selectAll("officeLeaves")
		.select([
			"users.name as userName",
			"users.email as userEmail",
			"reviewer.name as reviewerName",
			"reviewer.email as reviewerEmail",
		])
		.orderBy("officeLeaves.createdAt", "desc")
		.limit(5)
		.execute();

	return {
		usersData: [
			{
				id: "total_employees",
				title: "Total Karyawan",
				total: totalEmployees,
				trend: {
					value: totalEmployees - totalEmployeesYesterday,
					description:
						totalEmployees > totalEmployeesYesterday
							? "Meningkat dibanding kemarin"
							: totalEmployees < totalEmployeesYesterday
								? "Menurun dibanding kemarin"
								: "Sama seperti kemarin",
					trendType: getTrendType(totalEmployees - totalEmployeesYesterday),
				},
				iconKey: "users_double",
			},
			{
				id: "total_present",
				title: "Total Karyawan Hadir",
				total: totalPresent,
				trend: {
					value: totalPresent - totalPresentYesterday,
					description:
						totalPresent > totalPresentYesterday
							? "Meningkat dibanding kemarin"
							: totalPresent < totalPresentYesterday
								? "Menurun dibanding kemarin"
								: "Sama seperti kemarin",
					trendType: getTrendType(totalPresent - totalPresentYesterday),
				},
				iconKey: "users_double",
			},
			{
				id: "total_not_present",
				title: "Total Karyawan Tidak Hadir",
				total: totalNotPresent,
				trend: {
					value: totalNotPresent - totalNotPresentYesterday,
					description:
						totalNotPresent > totalNotPresentYesterday
							? "Meningkat dibanding kemarin"
							: totalNotPresent < totalNotPresentYesterday
								? "Menurun dibanding kemarin"
								: "Sama seperti kemarin",
					trendType: getTrendType(totalNotPresent - totalNotPresentYesterday),
				},
				iconKey: "users_double",
			},
			{
				id: "total_users_leave",
				title: "Total Karyawan Cuti",
				total: totalUsersLeave,
				trend: {
					value: totalUsersLeave - totalUsersLeaveYesterday,
					description:
						totalUsersLeave > totalUsersLeaveYesterday
							? "Meningkat dibanding kemarin"
							: totalUsersLeave < totalUsersLeaveYesterday
								? "Menurun dibanding kemarin"
								: "Sama seperti kemarin",
					trendType: getTrendType(totalUsersLeave - totalUsersLeaveYesterday),
				},
				iconKey: "users_double",
			},
		],
		attendanceData: [
			{
				id: "total_on_time",
				title: "Total Karyawan Datang Tepat Waktu",
				total: totalOnTime,
				trend: {
					value: totalOnTime - totalOnTimeYesterday,
					description:
						totalOnTime > totalOnTimeYesterday
							? "Meningkat dibanding kemarin"
							: totalOnTime < totalOnTimeYesterday
								? "Menurun dibanding kemarin"
								: "Sama seperti kemarin",
					trendType: getTrendType(totalOnTime - totalOnTimeYesterday),
				},
				iconKey: "timer",
			},
			{
				id: "total_late",
				title: "Total Karyawan Datang Terlambat",
				total: totalLate,
				trend: {
					value: totalLate - totalLateYesterday,
					description:
						totalLate > totalLateYesterday
							? "Meningkat dibanding kemarin"
							: totalLate < totalLateYesterday
								? "Menurun dibanding kemarin"
								: "Sama seperti kemarin",
					trendType: getTrendType(totalLate - totalLateYesterday),
				},
				iconKey: "timer",
			},
			{
				id: "total_checkout_on_time",
				title: "Total Karyawan Pulang Tepat Waktu",
				total: totalCheckoutOnTime,
				trend: {
					value: totalCheckoutOnTime - totalCheckoutOnTimeYesterday,
					description:
						totalCheckoutOnTime > totalCheckoutOnTimeYesterday
							? "Meningkat dibanding kemarin"
							: totalCheckoutOnTime < totalCheckoutOnTimeYesterday
								? "Menurun dibanding kemarin"
								: "Sama seperti kemarin",
					trendType: getTrendType(
						totalCheckoutOnTime - totalCheckoutOnTimeYesterday,
					),
				},
				iconKey: "timer",
			},
			{
				id: "total_checkout_early",
				title: "Total Karyawan Pulang Awal",
				total: totalCheckoutEarly,
				trend: {
					value: totalCheckoutEarly - totalCheckoutEarlyYesterday,
					description:
						totalCheckoutEarly > totalCheckoutEarlyYesterday
							? "Meningkat dibanding kemarin"
							: totalCheckoutEarly < totalCheckoutEarlyYesterday
								? "Menurun dibanding kemarin"
								: "Sama seperti kemarin",
					trendType: getTrendType(
						totalCheckoutEarly - totalCheckoutEarlyYesterday,
					),
				},
				iconKey: "timer",
			},
		],
		latestLeaveRequests,
		latestOfficeLeaves,
	};
};

export const getAttendanceStat = async (
	startDate?: string,
	endDate?: string,
): Promise<AttendanceStatResponseDTO[]> => {
	// Use today's date as default if no dates provided
	const today = new Date().toISOString().split("T")[0] as string;
	const defaultStartDate: string = startDate || today;
	const defaultEndDate: string = endDate || today;

	// Generate array of dates between startDate and endDate
	const dates: string[] = [];
	const start = new Date(defaultStartDate);
	const end = new Date(defaultEndDate);

	for (
		let date = new Date(start);
		date <= end;
		date.setDate(date.getDate() + 1)
	) {
		const dateStr = date.toISOString().split("T")[0];
		if (dateStr) {
			dates.push(dateStr);
		}
	}

	// Get total employees count (this should be consistent across dates for simplicity)
	const { total: totalEmployees } = await db
		.selectFrom("users")
		.select(sql<number>`count(*)`.as("total"))
		.executeTakeFirstOrThrow();

	// Get attendance data for all dates in the range
	const attendanceData = await db
		.selectFrom("attendanceLogs")
		.select([
			"logDate",
			sql<number>`count(distinct user_id)`.as("presentCount"),
		])
		.where("type", "=", "CHECK_IN")
		.where("logDate", ">=", defaultStartDate)
		.where("logDate", "<=", defaultEndDate)
		.groupBy("logDate")
		.execute();

	// Get leave data for all dates in the range - using a simpler approach
	const leaveData: { leaveDate: string; leaveCount: number }[] = [];

	// For each date, count users on leave
	for (const date of dates) {
		const { total: leaveCount } = await db
			.selectFrom("leaveRequests")
			.select(sql<number>`count(distinct user_id)`.as("total"))
			.where("status", "=", "APPROVED")
			.where("startDate", "<=", date)
			.where("endDate", ">=", date)
			.executeTakeFirstOrThrow();

		leaveData.push({ leaveDate: date, leaveCount });
	}

	// Create a map for quick lookup
	const attendanceMap = new Map<string, number>();
	attendanceData.forEach((row) => {
		attendanceMap.set(row.logDate, row.presentCount);
	});

	const leaveMap = new Map<string, number>();
	leaveData.forEach((row) => {
		leaveMap.set(row.leaveDate, row.leaveCount);
	});

	// Build the result array
	const result: AttendanceStatResponseDTO[] = dates.map((date) => {
		const present = attendanceMap.get(date) || 0;
		const leave = leaveMap.get(date) || 0;
		const absent = Math.max(0, totalEmployees - present - leave);

		return {
			date,
			total: totalEmployees,
			present,
			absent,
			leave,
		};
	});

	return result;
};
